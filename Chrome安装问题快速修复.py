#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome安装问题快速修复工具
解决"请检查文件完整性"错误

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-26
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import sys
from pathlib import Path
import threading

# 导入核心模块
try:
    from Chrome更新管理器 import chrome_updater
    from 主题管理器 import theme_manager, apply_theme
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ChromeInstallFixTool:
    """Chrome安装问题快速修复工具"""
    
    def __init__(self):
        """初始化修复工具"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.selected_file = None
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔧 Chrome安装问题快速修复")
        self.root.geometry("700x500")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔧 Chrome安装问题快速修复", 
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 15))
        
        # 问题说明
        problem_frame = tk.LabelFrame(main_frame, text="❌ 遇到的问题", 
                                    font=("Microsoft YaHei", 12))
        problem_frame.pack(fill=tk.X, pady=(0, 15))
        
        problem_text = """
如果您遇到"Chrome Portable安装失败，请检查文件完整性"的错误，
这个工具可以帮您诊断和解决问题。

常见原因：
• PAF文件下载不完整或损坏
• 文件大小异常（太小或为空）
• 文件格式不正确
• 网络下载中断导致文件截断
        """
        
        problem_label = tk.Label(problem_frame, text=problem_text.strip(),
                               font=("Microsoft YaHei", 10), justify=tk.LEFT)
        problem_label.pack(padx=15, pady=10, anchor=tk.W)
        
        # 解决方案
        solution_frame = tk.LabelFrame(main_frame, text="✅ 解决方案", 
                                     font=("Microsoft YaHei", 12))
        solution_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 按钮容器
        btn_container = tk.Frame(solution_frame)
        btn_container.pack(pady=15)
        
        # 第一行按钮
        btn_row1 = tk.Frame(btn_container)
        btn_row1.pack(pady=5)
        
        self.check_file_btn = tk.Button(btn_row1, text="🔍 检查当前PAF文件", 
                                      command=self.check_current_file, width=20,
                                      font=("Microsoft YaHei", 10))
        self.check_file_btn.pack(side=tk.LEFT, padx=5)
        
        self.download_new_btn = tk.Button(btn_row1, text="📥 重新下载Chrome", 
                                        command=self.download_new_chrome, width=20,
                                        font=("Microsoft YaHei", 10))
        self.download_new_btn.pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        btn_row2 = tk.Frame(btn_container)
        btn_row2.pack(pady=5)
        
        self.select_file_btn = tk.Button(btn_row2, text="📁 选择其他PAF文件", 
                                       command=self.select_other_file, width=20,
                                       font=("Microsoft YaHei", 10))
        self.select_file_btn.pack(side=tk.LEFT, padx=5)
        
        self.force_install_btn = tk.Button(btn_row2, text="⚡ 强制尝试安装", 
                                         command=self.force_install, width=20,
                                         font=("Microsoft YaHei", 10))
        self.force_install_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="📊 状态信息", 
                                   font=("Microsoft YaHei", 12))
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(status_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.status_text = tk.Text(text_frame, wrap=tk.WORD, 
                                 font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.root)
        
        # 初始化显示
        self.log_message("🔧 Chrome安装问题快速修复工具已启动")
        self.log_message("请选择下面的解决方案来修复安装问题")
        self.log_message("=" * 50)
    
    def log_message(self, message: str):
        """记录消息到状态显示区"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def check_current_file(self):
        """检查当前PAF文件"""
        self.log_message("\n🔍 检查当前下载的PAF文件...")
        
        def check_in_thread():
            try:
                # 查找最近下载的PAF文件
                temp_dir = chrome_updater.temp_dir
                paf_files = list(temp_dir.glob("*.paf.exe"))
                
                if not paf_files:
                    self.log_message("❌ 未找到下载的PAF文件")
                    self.log_message("💡 建议：重新下载Chrome Portable")
                    return
                
                # 检查最新的PAF文件
                latest_file = max(paf_files, key=lambda f: f.stat().st_mtime)
                self.log_message(f"📁 找到PAF文件: {latest_file.name}")
                
                # 使用改进的完整性检查
                integrity_result = chrome_updater._check_file_integrity(latest_file)
                
                self.log_message(f"📊 检查结果: {integrity_result['message']}")
                self.log_message(f"💡 建议: {integrity_result['suggestion']}")
                
                if integrity_result['valid']:
                    self.log_message("✅ 文件检查通过，可以尝试安装")
                    self.selected_file = latest_file
                else:
                    self.log_message("❌ 文件检查失败，需要重新下载")
                
            except Exception as e:
                self.log_message(f"❌ 检查失败: {e}")
        
        threading.Thread(target=check_in_thread, daemon=True).start()
    
    def download_new_chrome(self):
        """重新下载Chrome"""
        self.log_message("\n📥 启动Chrome下载器...")
        
        try:
            # 启动Chrome下载器
            import subprocess
            subprocess.Popen([sys.executable, "GoogleChrome下载器GUI.py"])
            self.log_message("✅ Chrome下载器已启动")
            self.log_message("💡 请在下载器中下载最新版本，然后回到这里重新检查")
            
        except Exception as e:
            self.log_message(f"❌ 启动下载器失败: {e}")
            self.log_message("💡 请手动运行 GoogleChrome下载器GUI.py")
    
    def select_other_file(self):
        """选择其他PAF文件"""
        self.log_message("\n📁 选择其他PAF文件...")
        
        file_path = filedialog.askopenfilename(
            title="选择Chrome Portable PAF文件",
            filetypes=[
                ("PAF文件", "*.paf.exe"),
                ("可执行文件", "*.exe"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            selected_file = Path(file_path)
            self.log_message(f"📁 已选择文件: {selected_file.name}")
            
            def check_in_thread():
                try:
                    # 检查文件完整性
                    integrity_result = chrome_updater._check_file_integrity(selected_file)
                    
                    self.log_message(f"📊 检查结果: {integrity_result['message']}")
                    self.log_message(f"💡 建议: {integrity_result['suggestion']}")
                    
                    if integrity_result['valid']:
                        self.log_message("✅ 文件检查通过，可以尝试安装")
                        self.selected_file = selected_file
                    else:
                        self.log_message("❌ 文件检查失败，请选择其他文件或重新下载")
                        
                except Exception as e:
                    self.log_message(f"❌ 检查失败: {e}")
            
            threading.Thread(target=check_in_thread, daemon=True).start()
    
    def force_install(self):
        """强制尝试安装"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择或检查PAF文件")
            return
        
        result = messagebox.askyesno("确认强制安装", 
                                   f"将强制尝试安装以下文件：\n{self.selected_file}\n\n"
                                   "注意：这可能会跳过某些安全检查。\n"
                                   "确定要继续吗？")
        if not result:
            return
        
        self.log_message("\n⚡ 开始强制安装...")
        
        def install_in_thread():
            try:
                def progress_callback(progress, message):
                    self.log_message(f"[{progress}%] {message}")
                
                # 设置进度回调
                chrome_updater.download_callback = progress_callback
                
                # 临时修改检查逻辑（跳过严格检查）
                original_check = chrome_updater._check_file_integrity
                
                def lenient_check(file_path):
                    """宽松的文件检查"""
                    if not file_path.exists():
                        return {"valid": False, "message": "文件不存在", "suggestion": "请选择有效文件"}
                    
                    file_size = file_path.stat().st_size
                    if file_size < 1024:  # 只检查是否为空文件
                        return {"valid": False, "message": "文件为空", "suggestion": "请重新下载"}
                    
                    return {"valid": True, "message": "强制检查通过", "suggestion": "尝试安装"}
                
                chrome_updater._check_file_integrity = lenient_check
                
                try:
                    # 执行安装
                    success = chrome_updater.install_chrome(self.selected_file)
                    
                    if success:
                        self.log_message("✅ 强制安装成功！")
                        messagebox.showinfo("安装成功", "Chrome安装成功！")
                    else:
                        self.log_message("❌ 强制安装失败")
                        messagebox.showerror("安装失败", "强制安装失败，请尝试重新下载文件。")
                        
                finally:
                    # 恢复原始检查逻辑
                    chrome_updater._check_file_integrity = original_check
                    
            except Exception as e:
                self.log_message(f"❌ 强制安装失败: {e}")
                messagebox.showerror("安装错误", f"强制安装过程中发生错误：{e}")
        
        threading.Thread(target=install_in_thread, daemon=True).start()
    
    def run(self):
        """运行修复工具"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔧 启动Chrome安装问题快速修复工具...")
    try:
        app = ChromeInstallFixTool()
        app.run()
    except Exception as e:
        print(f"❌ 修复工具启动失败: {e}")
        input("按回车键退出...")
