{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\浏览器实例\\1\\Chrome-bin\\138.0.7204.158\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\浏览器实例\\1\\Chrome-bin\\138.0.7204.158\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\浏览器实例\\1\\Chrome-bin\\138.0.7204.158\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\浏览器实例\\1\\Chrome-bin\\138.0.7204.158\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "42DD5CBC1629173CC8BECD0218102AA54DD2EA9A6318DD77A6A816A2A8468F12"}, "extensions": {"ui": {"developer_mode": "A465886E46184412869CD02A58AFE38EDD24686B57CA16F5B83981CADDCD4B28"}}, "homepage": "BE6028C4CE8BD9010B81357D11F2CE254FAA33D4F1CD4042CE100C01245EE814", "homepage_is_newtabpage": "018B85597EA211E359BC6108535EC0EDA725335506ACE3A15D1B158A25984461", "session": {"restore_on_startup": "24EFD2E1F8F6DD3B443C49EEA893E43EFD5CC91A2297DF1BE3EBDDC92306E0E6", "startup_urls": "086F36119E168FE064D670AF8402E5CA73ADD9292FB29D9E94E583A028CA924F"}}, "browser": {"show_home_button": "C9B0DAFF1D5EF3CBA224B9D838A5D7F205F20A1F8A8A1C12DAEAAD95A6B5C915"}, "default_search_provider_data": {"template_url_data": "E1C657B3B8D9FE1023632E2577E7056F103BCD72DA33F84EBFB1E9AF12FC4469"}, "enterprise_signin": {"policy_recovery_token": "2FC235DDEFC9DC91E9AA0BD516E924E6D8C2DFF7671D0FDB507159CA51BA01A1"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "78C12E0597B85B8155CDE6FB94284B3CF708A7BE440D6FC90F9DF919F40D58CD", "fignfifoniblkonapihmkfakmlgkbkcf": "03B518E56D5F4179CC75DBD7FD2109C96879DC82EA48F79C9CF3AC0AF71763F2", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "7652BA3F6CC2113BB601B1E2EC0F49F92A7295393842272070182B2037FA5112", "nkeimhogjdpnpccoofpliimaahmaaome": "773030F73E6314E9869067024B26AC757E21DD37B8C59CE8F2C8D906499AF9E9"}, "ui": {"developer_mode": "9C0A760353C9777695CF19989B69E43C143FFD788067112410817CB7E0CBC671"}}, "google": {"services": {"account_id": "BB3214790F223D507278D64AC1F722E39E8CC1AFD9538CF33D5001DEF6FBB6F5", "last_signed_in_username": "F941DBEA7C8D5B4B69CA0A44C393FC5B40CA8E39366A64885BEF3FAB2913EA96", "last_username": "7628939FFCE2AD31BCA6250271A12FBB7C43167DB67FA819C819F68B9D8A9FA3"}}, "homepage": "4B7B46AC42E24D6D17ED56C8C73B9CEC4F3C663DB20DF19DDFE50CD86F5FC60F", "homepage_is_newtabpage": "E3E860C02980157A14059299CB6CC14411B5FB3F29CC37CEA99ECCC49C61D6A3", "media": {"cdm": {"origin_data": "0CFD9108FA514D05380BE9734EE07E2C8AA65F787AD57BBBC400D97A7FB566F4"}, "storage_id_salt": "BEA1FFDB2716535C9297455ECACDE2249AC957ABE02C485DCF10AD95F8324094"}, "module_blocklist_cache_md5_digest": "CB0FB5928ED6B030F6A6334652AF8CA3288DB029E5F85445D255E2CFFF0E466A", "pinned_tabs": "1865BF5B3143133049EB3821CE1EC0B8FD81DDDFF811F318EA6515803B4A0CE0", "prefs": {"preference_reset_time": "6CE8E8729573F073B0E933952034BAE61CD10C1250E2F975962D7F2A2F92721A"}, "safebrowsing": {"incidents_sent": "6328D2ABDF12017F51C764411790A472D5283423C7F296863B9BDBD0018C20B9"}, "search_provider_overrides": "024EED9881EA13F5E5D552349E3CC2B8FD139CE043947AE702D15531A285DF9B", "session": {"restore_on_startup": "DD014520F423FE1D70AC956A3CE4AFAB392E2A8F193AB5B03CA8474276B5DC58", "startup_urls": "6A9D211D2460B6A1196AEF9FFD90227C7887B3EDD3C717F615B7ADC4C14EA3FB"}}, "super_mac": "C2E16E487D4C125DE62219AD0BFEFB3D26C8D61189573745163ED4C192A8841D"}}