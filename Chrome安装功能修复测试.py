#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome安装功能修复测试程序
测试修复后的Chrome安装功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-26
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import sys
import threading
from pathlib import Path

# 导入核心模块
try:
    from Chrome更新管理器 import chrome_updater
    from 主题管理器 import theme_manager, apply_theme
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ChromeInstallTestApp:
    """Chrome安装功能测试应用"""
    
    def __init__(self):
        """初始化测试应用"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.selected_file = None
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔧 Chrome安装功能修复测试")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔧 Chrome安装功能修复测试", 
                              font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 功能说明
        info_text = """
Chrome安装功能修复测试

修复内容：
• 改进PAF文件安装处理逻辑
• 添加多种安装参数尝试
• 改进错误处理和用户反馈
• 添加安装验证机制
• 支持静默安装和交互式安装

测试功能：
• 选择PAF安装文件
• 测试安装过程
• 验证安装结果
• 查看详细日志
        """
        
        info_label = tk.Label(main_frame, text=info_text.strip(),
                             font=("Microsoft YaHei", 11), justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = tk.LabelFrame(main_frame, text="安装文件选择", 
                                 font=("Microsoft YaHei", 12))
        file_frame.pack(fill=tk.X, pady=(0, 20))
        
        file_select_frame = tk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("请选择Chrome Portable PAF安装文件...")
        
        file_entry = tk.Entry(file_select_frame, textvariable=self.file_path_var,
                             font=("Microsoft YaHei", 10), state="readonly")
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        select_btn = tk.Button(file_select_frame, text="📁 选择文件",
                              command=self.select_file, width=12)
        select_btn.pack(side=tk.RIGHT)
        
        # 当前状态显示
        status_frame = tk.LabelFrame(main_frame, text="当前状态", 
                                   font=("Microsoft YaHei", 12))
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.current_version_label = tk.Label(status_frame, text="当前版本: 检测中...", 
                                            font=("Microsoft YaHei", 11))
        self.current_version_label.pack(anchor=tk.W, padx=15, pady=5)
        
        self.chrome_dir_label = tk.Label(status_frame, text=f"Chrome目录: {chrome_updater.chrome_dir}", 
                                       font=("Microsoft YaHei", 11))
        self.chrome_dir_label.pack(anchor=tk.W, padx=15, pady=5)
        
        # 测试按钮区域
        test_frame = tk.LabelFrame(main_frame, text="测试操作", 
                                 font=("Microsoft YaHei", 12))
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        btn_container = tk.Frame(test_frame)
        btn_container.pack(pady=15)
        
        self.test_install_btn = tk.Button(btn_container, text="🔧 测试安装功能", 
                                        command=self.test_install, width=18,
                                        font=("Microsoft YaHei", 11))
        self.test_install_btn.pack(side=tk.LEFT, padx=10)
        
        self.verify_btn = tk.Button(btn_container, text="✅ 验证安装", 
                                  command=self.verify_installation, width=15,
                                  font=("Microsoft YaHei", 11))
        self.verify_btn.pack(side=tk.LEFT, padx=10)
        
        self.refresh_btn = tk.Button(btn_container, text="🔄 刷新状态", 
                                   command=self.refresh_status, width=15,
                                   font=("Microsoft YaHei", 11))
        self.refresh_btn.pack(side=tk.LEFT, padx=10)
        
        # 结果显示
        result_frame = tk.LabelFrame(main_frame, text="测试日志", 
                                   font=("Microsoft YaHei", 12))
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, 
                                 font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.root)
        
        # 应用主题到所有组件
        widgets_to_theme = [
            (main_frame, "frame"),
            (title_label, "label"),
            (info_label, "label"),
            (file_frame, "frame"),
            (file_select_frame, "frame"),
            (file_entry, "entry"),
            (select_btn, "button"),
            (status_frame, "frame"),
            (self.current_version_label, "label"),
            (self.chrome_dir_label, "label"),
            (test_frame, "frame"),
            (btn_container, "frame"),
            (self.test_install_btn, "button"),
            (self.verify_btn, "button"),
            (self.refresh_btn, "button"),
            (result_frame, "frame"),
            (text_frame, "frame"),
            (self.result_text, "text")
        ]
        
        for widget, widget_type in widgets_to_theme:
            apply_theme(widget, widget_type)
        
        # 初始化检查
        self.refresh_status()
    
    def log_message(self, message: str):
        """记录消息到结果显示区"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def select_file(self):
        """选择PAF安装文件"""
        file_path = filedialog.askopenfilename(
            title="选择Chrome Portable PAF安装文件",
            filetypes=[
                ("PAF文件", "*.paf.exe"),
                ("可执行文件", "*.exe"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.selected_file = Path(file_path)
            self.file_path_var.set(str(self.selected_file))
            self.log_message(f"📁 已选择文件: {self.selected_file}")
            
            # 显示文件信息
            try:
                file_size = self.selected_file.stat().st_size
                size_mb = file_size / (1024 * 1024)
                self.log_message(f"📊 文件大小: {size_mb:.1f} MB")
            except Exception as e:
                self.log_message(f"❌ 获取文件信息失败: {e}")
    
    def refresh_status(self):
        """刷新状态显示"""
        def check_in_thread():
            try:
                self.log_message("🔄 刷新状态信息...")
                
                # 获取当前版本
                current = chrome_updater.get_current_version()
                if current:
                    self.current_version_label.config(text=f"当前版本: {current}")
                    self.log_message(f"✅ 当前版本: {current}")
                else:
                    self.current_version_label.config(text="当前版本: 未检测到")
                    self.log_message("❌ 未检测到Chrome Portable")
                
            except Exception as e:
                self.log_message(f"❌ 状态刷新失败: {e}")
        
        # 在后台线程中执行
        threading.Thread(target=check_in_thread, daemon=True).start()
    
    def test_install(self):
        """测试安装功能"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择PAF安装文件")
            return
        
        if not self.selected_file.exists():
            messagebox.showerror("错误", "选择的文件不存在")
            return
        
        # 确认测试
        result = messagebox.askyesno("确认测试", 
                                   f"将测试安装以下文件：\n{self.selected_file}\n\n"
                                   "注意：这将实际安装Chrome Portable。\n"
                                   "确定要继续吗？")
        if not result:
            return
        
        self.log_message("🔧 开始测试安装功能...")
        
        def test_in_thread():
            try:
                def progress_callback(progress, message):
                    self.log_message(f"[{progress}%] {message}")
                
                # 设置进度回调
                chrome_updater.download_callback = progress_callback
                
                # 执行安装
                success = chrome_updater.install_chrome(self.selected_file)
                
                if success:
                    self.log_message("✅ 安装测试成功！")
                    messagebox.showinfo("测试成功", "Chrome安装功能测试成功！")
                    # 刷新状态
                    self.refresh_status()
                else:
                    self.log_message("❌ 安装测试失败")
                    messagebox.showerror("测试失败", "Chrome安装功能测试失败，请查看日志了解详情。")
                    
            except Exception as e:
                self.log_message(f"❌ 安装测试失败: {e}")
                messagebox.showerror("测试错误", f"安装测试过程中发生错误：{e}")
        
        # 在后台线程中执行测试
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def verify_installation(self):
        """验证安装结果"""
        self.log_message("✅ 验证安装结果...")
        
        def verify_in_thread():
            try:
                success = chrome_updater._verify_installation()
                
                if success:
                    self.log_message("✅ 安装验证成功")
                    messagebox.showinfo("验证成功", "Chrome安装验证成功！")
                else:
                    self.log_message("❌ 安装验证失败")
                    messagebox.showwarning("验证失败", "Chrome安装验证失败，请检查安装是否完整。")
                
                # 刷新状态
                self.refresh_status()
                
            except Exception as e:
                self.log_message(f"❌ 验证过程失败: {e}")
                messagebox.showerror("验证错误", f"验证过程中发生错误：{e}")
        
        # 在后台线程中执行验证
        threading.Thread(target=verify_in_thread, daemon=True).start()
    
    def run(self):
        """运行测试应用"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔧 启动Chrome安装功能修复测试...")
    try:
        app = ChromeInstallTestApp()
        app.run()
    except Exception as e:
        print(f"❌ 测试应用启动失败: {e}")
        input("按回车键退出...")
