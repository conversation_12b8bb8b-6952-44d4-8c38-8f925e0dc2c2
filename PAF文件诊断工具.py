#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAF文件诊断工具
用于检查Chrome Portable PAF文件的完整性和有效性

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-26
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from pathlib import Path
import sys
import threading

class PAFDiagnosticTool:
    """PAF文件诊断工具"""
    
    def __init__(self):
        """初始化诊断工具"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.selected_file = None
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔍 PAF文件诊断工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔍 PAF文件诊断工具", 
                              font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_text = """
此工具用于诊断Chrome Portable PAF文件的完整性和有效性。
可以帮助您了解PAF文件是否损坏、大小是否正常、格式是否正确等。
        """
        
        info_label = tk.Label(main_frame, text=info_text.strip(),
                             font=("Microsoft YaHei", 11), justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = tk.LabelFrame(main_frame, text="选择PAF文件", 
                                 font=("Microsoft YaHei", 12))
        file_frame.pack(fill=tk.X, pady=(0, 20))
        
        file_select_frame = tk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("请选择要诊断的PAF文件...")
        
        file_entry = tk.Entry(file_select_frame, textvariable=self.file_path_var,
                             font=("Microsoft YaHei", 10), state="readonly")
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        select_btn = tk.Button(file_select_frame, text="📁 选择文件",
                              command=self.select_file, width=12)
        select_btn.pack(side=tk.RIGHT)
        
        # 诊断按钮
        diagnose_btn = tk.Button(main_frame, text="🔍 开始诊断", 
                               command=self.start_diagnosis, width=20,
                               font=("Microsoft YaHei", 12))
        diagnose_btn.pack(pady=10)
        
        # 结果显示
        result_frame = tk.LabelFrame(main_frame, text="诊断结果", 
                                   font=("Microsoft YaHei", 12))
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, 
                                                   font=("Consolas", 9))
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def log_message(self, message: str):
        """记录消息到结果显示区"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def select_file(self):
        """选择PAF文件"""
        file_path = filedialog.askopenfilename(
            title="选择Chrome Portable PAF文件",
            filetypes=[
                ("PAF文件", "*.paf.exe"),
                ("可执行文件", "*.exe"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.selected_file = Path(file_path)
            self.file_path_var.set(str(self.selected_file))
            self.log_message(f"📁 已选择文件: {self.selected_file}")
    
    def start_diagnosis(self):
        """开始诊断"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择PAF文件")
            return
        
        if not self.selected_file.exists():
            messagebox.showerror("错误", "选择的文件不存在")
            return
        
        self.result_text.delete(1.0, tk.END)
        self.log_message("🔍 开始PAF文件诊断...")
        self.log_message("=" * 60)
        
        # 在后台线程中执行诊断
        threading.Thread(target=self.perform_diagnosis, daemon=True).start()
    
    def perform_diagnosis(self):
        """执行诊断"""
        try:
            file_path = self.selected_file
            
            # 1. 基本文件信息
            self.log_message("📊 基本文件信息:")
            self.log_message(f"   文件名: {file_path.name}")
            self.log_message(f"   文件路径: {file_path}")
            
            # 2. 文件大小检查
            file_size = file_path.stat().st_size
            size_mb = file_size / (1024 * 1024)
            self.log_message(f"   文件大小: {file_size:,} bytes ({size_mb:.2f} MB)")
            
            # 判断文件大小是否合理
            if file_size < 1024:  # 小于1KB
                self.log_message("   ❌ 文件大小异常：小于1KB，可能是空文件或损坏")
            elif file_size < 1024 * 1024:  # 小于1MB
                self.log_message("   ⚠️ 文件大小警告：小于1MB，可能不是完整的PAF文件")
            elif file_size < 10 * 1024 * 1024:  # 小于10MB
                self.log_message("   ⚠️ 文件大小警告：小于10MB，可能是精简版或损坏")
            elif file_size < 50 * 1024 * 1024:  # 小于50MB
                self.log_message("   ⚠️ 文件大小注意：小于50MB，可能是旧版本或精简版")
            else:
                self.log_message("   ✅ 文件大小正常：符合Chrome Portable PAF文件的预期大小")
            
            # 3. 文件扩展名检查
            self.log_message("\n🏷️ 文件格式检查:")
            if file_path.suffix.lower() == '.exe':
                if '.paf.' in file_path.name.lower():
                    self.log_message("   ✅ 文件扩展名正确：.paf.exe格式")
                else:
                    self.log_message("   ⚠️ 文件扩展名警告：是.exe文件但不包含.paf标识")
            else:
                self.log_message(f"   ❌ 文件扩展名错误：{file_path.suffix}，应该是.exe")
            
            # 4. 文件头检查
            self.log_message("\n🔍 文件头检查:")
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(1024)  # 读取前1KB
                
                # 检查PE文件头
                if header[:2] == b'MZ':
                    self.log_message("   ✅ PE文件头正确：文件是有效的Windows可执行文件")
                    
                    # 查找PE签名
                    pe_offset = int.from_bytes(header[60:64], 'little')
                    if pe_offset < len(header) - 4:
                        pe_signature = header[pe_offset:pe_offset+4]
                        if pe_signature == b'PE\x00\x00':
                            self.log_message("   ✅ PE签名正确：文件结构完整")
                        else:
                            self.log_message("   ⚠️ PE签名异常：可能文件损坏")
                    else:
                        self.log_message("   ⚠️ PE偏移异常：无法验证PE签名")
                else:
                    self.log_message("   ❌ 文件头错误：不是有效的Windows可执行文件")
                
                # 检查是否包含PortableApps相关字符串
                header_str = header.decode('latin-1', errors='ignore').lower()
                if 'portable' in header_str or 'paf' in header_str:
                    self.log_message("   ✅ 包含Portable相关标识")
                else:
                    self.log_message("   ⚠️ 未发现Portable相关标识")
                    
            except Exception as e:
                self.log_message(f"   ❌ 文件头检查失败: {e}")
            
            # 5. 文件可读性检查
            self.log_message("\n📖 文件可读性检查:")
            try:
                with open(file_path, 'rb') as f:
                    # 尝试读取文件的不同部分
                    f.seek(0)
                    start_chunk = f.read(1024)
                    
                    f.seek(file_size // 2)
                    middle_chunk = f.read(1024)
                    
                    f.seek(max(0, file_size - 1024))
                    end_chunk = f.read(1024)
                
                if len(start_chunk) > 0 and len(end_chunk) > 0:
                    self.log_message("   ✅ 文件可读性正常：能够读取文件的开始、中间和结尾")
                else:
                    self.log_message("   ❌ 文件可读性异常：无法正常读取文件内容")
                    
            except Exception as e:
                self.log_message(f"   ❌ 文件可读性检查失败: {e}")
            
            # 6. 建议和解决方案
            self.log_message("\n💡 诊断建议:")
            
            if file_size < 1024 * 1024:  # 小于1MB
                self.log_message("   🔧 建议重新下载PAF文件，当前文件可能不完整")
                self.log_message("   📥 推荐从PortableApps官网下载最新版本")
            elif file_size < 50 * 1024 * 1024:  # 小于50MB
                self.log_message("   🔧 文件可能是旧版本或精简版，建议下载完整版")
            else:
                self.log_message("   ✅ 文件大小正常，可以尝试安装")
            
            if not '.paf.' in file_path.name.lower():
                self.log_message("   🔧 确保文件是从PortableApps官网下载的PAF格式")
            
            # 7. 推荐的下载源
            self.log_message("\n📥 推荐下载源:")
            self.log_message("   🌐 PortableApps官网: https://portableapps.com/apps/internet/google_chrome_portable")
            self.log_message("   🌐 备用下载: 使用项目内置的GoogleChrome下载器")
            
            self.log_message("\n" + "=" * 60)
            self.log_message("🎉 PAF文件诊断完成")
            
        except Exception as e:
            self.log_message(f"❌ 诊断过程失败: {e}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
    
    def run(self):
        """运行诊断工具"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔍 启动PAF文件诊断工具...")
    try:
        app = PAFDiagnosticTool()
        app.run()
    except Exception as e:
        print(f"❌ 诊断工具启动失败: {e}")
        input("按回车键退出...")
